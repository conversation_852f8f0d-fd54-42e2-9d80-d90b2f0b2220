-- SQL скрипт для добавления специальной акции для поп-апа
-- Выполнить в SQLite базе данных PocketBase

INSERT OR IGNORE INTO promos (
  id,
  title,
  subtitle,
  slug,
  content,
  is_active,
  is_featured,
  sort_order,
  meta_title,
  meta_description,
  created,
  updated
) VALUES (
  'popup_promo_001',
  'Только сегодня!',
  'Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.',
  'popup-promo',
  '<div class="text-center">
    <h2 class="text-2xl font-bold text-olive-800 mb-4">Только сегодня!</h2>
    <p class="text-lg text-olive-700 mb-6">
      Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.
    </p>
    <div class="bg-olive-50 p-4 rounded-lg mb-6">
      <p class="text-olive-800 font-semibold">
        🎁 Что входит в бесплатный осмотр:
      </p>
      <ul class="text-olive-700 mt-2 text-left">
        <li>• Консультация врача-стоматолога</li>
        <li>• Составление плана лечения</li>
        <li>• Рекомендации по уходу за полостью рта</li>
      </ul>
    </div>
    <p class="text-sm text-olive-600">
      ⏰ Предложение действует только сегодня!
    </p>
  </div>',
  1, -- is_active = true
  0, -- is_featured = false
  999, -- sort_order (в конце списка)
  'Специальное предложение - Бесплатный осмотр',
  'Запишитесь на бесплатный осмотр и получите скидку 10% на первое лечение',
  datetime('now'),
  datetime('now')
);
