#!/usr/bin/env node

/**
 * Скрипт для создания специальной акции для поп-апа
 * Запускается из директории backend
 */

const PocketBase = require('pocketbase');

async function createPopupPromo() {
  const pb = new PocketBase('http://localhost:8090');
  
  try {
    // Авторизация как администратор
    await pb.admins.authWithPassword(
      process.env.ADMIN_EMAIL || '<EMAIL>',
      process.env.ADMIN_PASSWORD || 'admin123456'
    );
    
    console.log('✅ Успешная авторизация в PocketBase');
    
    // Проверяем, есть ли уже акция с slug 'popup-promo'
    try {
      const existingPromo = await pb.collection('promos').getFirstListItem('slug="popup-promo"');
      console.log('⚠️  Акция для поп-апа уже существует:', existingPromo.title);
      return;
    } catch (error) {
      // Акция не найдена, создаем новую
    }
    
    // Создаем новую акцию для поп-апа
    const promoData = {
      title: 'Только сегодня!',
      subtitle: 'Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.',
      slug: 'popup-promo',
      content: `
        <div class="text-center">
          <h2 class="text-2xl font-bold text-olive-800 mb-4">Только сегодня!</h2>
          <p class="text-lg text-olive-700 mb-6">
            Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.
          </p>
          <div class="bg-olive-50 p-4 rounded-lg mb-6">
            <p class="text-olive-800 font-semibold">
              🎁 Что входит в бесплатный осмотр:
            </p>
            <ul class="text-olive-700 mt-2 text-left">
              <li>• Консультация врача-стоматолога</li>
              <li>• Составление плана лечения</li>
              <li>• Рекомендации по уходу за полостью рта</li>
            </ul>
          </div>
          <p class="text-sm text-olive-600">
            ⏰ Предложение действует только сегодня!
          </p>
        </div>
      `,
      is_active: true,
      is_featured: false,
      sort_order: 999, // Ставим в конец списка, чтобы не мешала в обычном отображении
      meta_title: 'Специальное предложение - Бесплатный осмотр',
      meta_description: 'Запишитесь на бесплатный осмотр и получите скидку 10% на первое лечение'
    };
    
    const newPromo = await pb.collection('promos').create(promoData);
    
    console.log('✅ Специальная акция для поп-апа успешно создана:');
    console.log('   ID:', newPromo.id);
    console.log('   Title:', newPromo.title);
    console.log('   Slug:', newPromo.slug);
    
  } catch (error) {
    console.error('❌ Ошибка при создании акции:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
createPopupPromo();
