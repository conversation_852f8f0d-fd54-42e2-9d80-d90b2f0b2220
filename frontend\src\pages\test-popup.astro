---
import Layout from '@/layouts/Layout.astro'
---

<Layout title="Тест поп-апа" description="Страница для тестирования акционного поп-апа">
  <div class="container mx-auto px-4 py-16">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-4xl font-bold text-center mb-8">Тест акционного поп-апа</h1>
      
      <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-semibold mb-4">Как протестировать поп-ап:</h2>
        <ul class="space-y-3 text-lg">
          <li class="flex items-start">
            <span class="text-olive-600 font-bold mr-2">1.</span>
            <span>Прокрутите страницу вниз на 50% или больше</span>
          </li>
          <li class="flex items-start">
            <span class="text-olive-600 font-bold mr-2">2.</span>
            <span>Попробуйте закрыть вкладку или переместите мышь к верхней части экрана</span>
          </li>
          <li class="flex items-start">
            <span class="text-olive-600 font-bold mr-2">3.</span>
            <span>Поп-ап должен появиться с анимацией</span>
          </li>
        </ul>
      </div>

      <div class="bg-olive-50 rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-3">Сброс ограничений для тестирования:</h3>
        <p class="mb-4">Если поп-ап уже был показан, нажмите кнопку ниже для сброса:</p>
        <button 
          id="reset-popup" 
          class="bg-olive-600 hover:bg-olive-700 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Сбросить ограничения поп-апа
        </button>
      </div>

      <!-- Контент для прокрутки -->
      <div class="space-y-8">
        {Array.from({ length: 10 }, (_, i) => (
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold mb-3">Секция {i + 1}</h3>
            <p class="text-gray-700 leading-relaxed">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor 
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis 
              nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 
              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore 
              eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, 
              sunt in culpa qui officia deserunt mollit anim id est laborum.
            </p>
          </div>
        ))}
      </div>

      <div class="text-center mt-16">
        <p class="text-gray-600">Прокрутите до этого места, чтобы активировать поп-ап по прокрутке</p>
      </div>
    </div>
  </div>

  <script>
    // Скрипт для сброса ограничений поп-апа
    document.addEventListener('DOMContentLoaded', () => {
      const resetButton = document.getElementById('reset-popup')
      if (resetButton) {
        resetButton.addEventListener('click', () => {
          localStorage.removeItem('promo-popup-timestamp')
          alert('Ограничения поп-апа сброшены! Теперь можно тестировать заново.')
        })
      }
    })
  </script>
</Layout>
